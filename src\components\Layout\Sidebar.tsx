import React from 'react';
import { FileSelector } from '../Menu/FileSelector';
import { FolderSelector } from '../Menu/FolderSelector';
import { FontSizeSelector } from '../Menu/FontSizeSelector';
import { Settings, HelpCircle } from 'lucide-react';

interface SidebarProps {
  onFileSelect: (file: File) => void;
  onFolderSelect: (files: FileList) => void;
  fileLoading: boolean;
  mediaCount: number;
  chatLoaded: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({
  onFileSelect,
  onFolderSelect,
  fileLoading,
  mediaCount,
  chatLoaded
}) => {
  return (
    <aside className="w-80 bg-gray-100 border-r border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800">Panel de Control</h2>
        <p className="text-sm text-gray-600">Carga tu chat y archivos multimedia</p>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 p-4 space-y-6 overflow-y-auto">
        {/* File Selector */}
        <FileSelector 
          onFileSelect={onFileSelect}
          loading={fileLoading}
        />
        
        {/* Folder Selector */}
        <FolderSelector
          onFolderSelect={onFolderSelect}
          mediaCount={mediaCount}
          disabled={!chatLoaded}
        />

        {/* Font Size Selector */}
        <FontSizeSelector />
        
        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
            <HelpCircle className="w-4 h-4" />
            Instrucciones
          </h4>
          <div className="text-sm text-blue-700 space-y-2">
            <p>1. Exporta tu chat desde WhatsApp (incluir multimedia)</p>
            <p>2. Selecciona el archivo .txt del chat</p>
            <p>3. Selecciona la carpeta con los archivos multimedia</p>
            <p>4. ¡Disfruta visualizando tu chat!</p>
          </div>

          <div className="mt-3 pt-3 border-t border-blue-200">
            <h5 className="font-medium text-blue-800 mb-1">Formatos soportados:</h5>
            <div className="text-xs text-blue-600 space-y-1">
              <p>• <code>15/01/24 9:00 a. m. - Usuario: mensaje</code></p>
              <p>• <code>27/7/23, 11:20 a. m. - Usuario: mensaje</code></p>
              <p>• <code>1/2/24, 3:45 PM - Usuario: mensaje</code></p>
              <p>• <code>02.03.24, 14:30 - Usuario: mensaje</code></p>
              <p>• Y más formatos automáticamente detectados</p>
            </div>
          </div>
        </div>
        
        {/* Status */}
        {chatLoaded && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-800 mb-2">Estado</h4>
            <div className="text-sm text-green-700 space-y-1">
              <p>✅ Chat cargado correctamente</p>
              <p>📁 {mediaCount} archivos multimedia disponibles</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Sidebar Footer */}
      <div className="p-4 border-t border-gray-200">
        <button className="w-full flex items-center justify-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors">
          <Settings className="w-4 h-4" />
          Configuración
        </button>
      </div>
    </aside>
  );
};
