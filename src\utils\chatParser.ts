import { ChatMessage, ParsedLine } from '../types/chat';

export const parseWhatsAppChat = (content: string): ChatMessage[] => {
  const lines = content.split('\n').filter(line => line.trim());
  const messages: ChatMessage[] = [];
  let parsedCount = 0;
  let skippedLines: string[] = [];
  let currentMessage: ChatMessage | null = null;

  console.log(`🔍 Iniciando parser de WhatsApp. Total de líneas: ${lines.length}`);

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const parsed = parseLine(line);

    if (parsed) {
      // Si hay un mensaje anterior pendiente, guardarlo
      if (currentMessage) {
        messages.push(currentMessage);
      }

      // Crear nuevo mensaje
      currentMessage = {
        id: generateId(),
        timestamp: parsed.timestamp,
        sender: parsed.sender,
        content: parsed.content,
        type: parsed.isMedia ? parsed.mediaType! : 'text',
        fileName: parsed.fileName,
      };

      parsedCount++;

      // Log del primer mensaje para verificar el formato detectado
      if (parsedCount === 1) {
        console.log(`✅ Primer mensaje parseado correctamente:`, {
          timestamp: parsed.timestamp,
          sender: parsed.sender,
          content: parsed.content.substring(0, 50) + '...'
        });
      }
    } else {
      // Si no se pudo parsear como nuevo mensaje, podría ser continuación del anterior
      if (currentMessage && line.trim() && !isSystemMessage(line)) {
        // Agregar como continuación del mensaje anterior
        currentMessage.content += '\n' + line.trim();
      } else if (line.length > 10) {
        // Guardar líneas que no se pudieron parsear para debugging
        skippedLines.push(line);
      }
    }
  }

  // Agregar el último mensaje si existe
  if (currentMessage) {
    messages.push(currentMessage);
  }

  console.log(`📊 Resultado del parser:`, {
    totalLines: lines.length,
    parsedMessages: messages.length,
    skippedLines: skippedLines.length,
    successRate: `${((messages.length / lines.length) * 100).toFixed(1)}%`
  });

  // Mostrar algunas líneas que no se pudieron parsear para debugging
  if (skippedLines.length > 0) {
    console.log(`⚠️ Ejemplos de líneas no parseadas:`, skippedLines.slice(0, 3));
  }

  return messages;
};

// Función para detectar mensajes del sistema que deben ignorarse
const isSystemMessage = (line: string): boolean => {
  const systemPatterns = [
    /se unió usando el enlace de invitación/i,
    /salió/i,
    /cambió el nombre del grupo/i,
    /cambió la descripción del grupo/i,
    /agregó a/i,
    /eliminó a/i,
    /Los mensajes están cifrados de extremo a extremo/i,
    /Messages are end-to-end encrypted/i,
    /creó el grupo/i,
    /created group/i
  ];

  return systemPatterns.some(pattern => pattern.test(line));
};

const parseLine = (line: string): ParsedLine | null => {
  // Intentar múltiples formatos de WhatsApp
  const formats = [
    // Formato 1: "3/05/24 12:56 p. m. - Diana Muñeca: Cuánto te testo"
    /^(\d{1,2}\/\d{1,2}\/\d{2,4}\s+\d{1,2}:\d{2}\s+[ap]\.\s+m\.)\s+-\s+([^:]+):\s+(.+)$/,

    // Formato 2: "27/7/23, 11:20 a. m. - Diana Cartucho: 2 litros salio"
    /^(\d{1,2}\/\d{1,2}\/\d{2,4},\s+\d{1,2}:\d{2}\s+[ap]\.\s+m\.)\s+-\s+([^:]+):\s+(.+)$/,

    // Formato 3: "01/11/24 10:01 a. m. - nombre del usuario: aquí el mensaje"
    /^(\d{1,2}\/\d{1,2}\/\d{2,4}\s+\d{1,2}:\d{2}\s+[ap]\.\s+m\.)\s+-\s+([^:]+):\s+(.+)$/,

    // Formato 4: "27/7/23, 11:20 AM - Diana Cartucho: mensaje" (formato inglés)
    /^(\d{1,2}\/\d{1,2}\/\d{2,4},\s+\d{1,2}:\d{2}\s+[AP]M)\s+-\s+([^:]+):\s+(.+)$/,

    // Formato 5: "2023-07-27 11:20:30 - Diana Cartucho: mensaje" (formato ISO)
    /^(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}:\d{2})\s+-\s+([^:]+):\s+(.+)$/,

    // Formato 6: "[27/7/23 11:20:30] Diana Cartucho: mensaje" (formato con corchetes)
    /^\[(\d{1,2}\/\d{1,2}\/\d{2,4}\s+\d{1,2}:\d{2}:\d{2})\]\s+([^:]+):\s+(.+)$/,

    // Formato 7: "27/7/23 11:20 - Diana Cartucho: mensaje" (sin AM/PM)
    /^(\d{1,2}\/\d{1,2}\/\d{2,4}\s+\d{1,2}:\d{2})\s+-\s+([^:]+):\s+(.+)$/,

    // Formato 8: "27.7.23, 11:20 - Diana Cartucho: mensaje" (puntos en fecha)
    /^(\d{1,2}\.\d{1,2}\.\d{2,4},?\s+\d{1,2}:\d{2}(?:\s+[ap]\.\s+m\.)?)\s+-\s+([^:]+):\s+(.+)$/i
  ];

  let match = null;
  let timestamp = '';
  let sender = '';
  let content = '';

  // Probar cada formato hasta encontrar uno que funcione
  for (const regex of formats) {
    match = line.match(regex);
    if (match) {
      [, timestamp, sender, content] = match;
      break;
    }
  }

  if (!match) return null;

  // Limpiar espacios extra del sender
  sender = sender.trim();
  content = content.trim();

  // Detectar archivos multimedia con múltiples patrones
  const mediaPatterns = [
    // Español: "archivo adjunto"
    /‎(.+\.(jpg|jpeg|png|gif|webp|opus|mp4|avi|mov|pdf|doc|docx))\s+\(archivo adjunto\)/i,
    // Inglés: "file attached" o "attached"
    /‎(.+\.(jpg|jpeg|png|gif|webp|opus|mp4|avi|mov|pdf|doc|docx))\s+\(file attached\)/i,
    /‎(.+\.(jpg|jpeg|png|gif|webp|opus|mp4|avi|mov|pdf|doc|docx))\s+\(attached\)/i,
    // Portugués: "arquivo anexado"
    /‎(.+\.(jpg|jpeg|png|gif|webp|opus|mp4|avi|mov|pdf|doc|docx))\s+\(arquivo anexado\)/i,
    // Solo el nombre del archivo con extensión multimedia
    /^‎(.+\.(jpg|jpeg|png|gif|webp|opus|mp4|avi|mov))$/i,
    // Formato sin símbolo especial
    /(.+\.(jpg|jpeg|png|gif|webp|opus|mp4|avi|mov))\s+\(archivo adjunto\)/i
  ];

  let mediaMatch = null;
  for (const pattern of mediaPatterns) {
    mediaMatch = content.match(pattern);
    if (mediaMatch) break;
  }

  if (mediaMatch) {
    const fileName = mediaMatch[1];
    const extension = mediaMatch[2].toLowerCase();

    let mediaType: 'image' | 'audio' | 'video' | 'sticker';
    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension)) {
      mediaType = 'image';
    } else if (['webp'].includes(extension)) {
      mediaType = 'sticker';
    } else if (['opus', 'mp3', 'wav', 'aac', 'm4a'].includes(extension)) {
      mediaType = 'audio';
    } else if (['mp4', 'avi', 'mov', '3gp', 'mkv'].includes(extension)) {
      mediaType = 'video';
    } else {
      mediaType = 'image'; // Por defecto
    }

    return {
      timestamp,
      sender,
      content: fileName,
      isMedia: true,
      fileName,
      mediaType
    };
  }

  return {
    timestamp,
    sender,
    content,
    isMedia: false
  };
};

const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};
