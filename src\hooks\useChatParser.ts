import { useState, useCallback, useMemo } from 'react';
import { ChatMessage, ChatData } from '../types/chat';
import { parseWhatsAppChat } from '../utils/chatParser';

interface UseChatParserReturn {
  chatData: ChatData | null;
  parseChat: (content: string, fileName: string) => void;
  clearChat: () => void;
  messageCount: number;
  mediaCount: number;
}

export const useChatParser = (): UseChatParserReturn => {
  const [chatData, setChatData] = useState<ChatData | null>(null);

  const parseChat = useCallback((content: string, fileName: string) => {
    try {
      console.log(`🚀 Parseando archivo: ${fileName}`);
      console.log(`📄 Contenido (primeras 3 líneas):`, content.split('\n').slice(0, 3));

      const messages = parseWhatsAppChat(content);
      const title = fileName.replace('.txt', '').replace('Chat de WhatsApp con ', '');

      console.log(`✅ Parser completado. Mensajes encontrados: ${messages.length}`);

      setChatData({
        title,
        messages
      });
    } catch (error) {
      console.error('❌ Error al parsear el chat:', error);
      setChatData(null);
    }
  }, []);

  const clearChat = useCallback(() => {
    setChatData(null);
  }, []);

  const messageCount = useMemo(() => {
    return chatData?.messages.length || 0;
  }, [chatData]);

  const mediaCount = useMemo(() => {
    return chatData?.messages.filter(msg => msg.type !== 'text').length || 0;
  }, [chatData]);

  return {
    chatData,
    parseChat,
    clearChat,
    messageCount,
    mediaCount
  };
};
